from fastapi import APIRouter

from api.api_v1.endpoints import (
    payment,
    transaction_status,
    survey,
    transaction_history,
)

api_router = APIRouter()
api_router.include_router(payment.router, prefix="", tags=["payment"])
api_router.include_router(survey.router, prefix="", tags=["survey"])
api_router.include_router(
    transaction_history.router, prefix="/history", tags=["history"]
)
api_router.include_router(
    transaction_status.router, prefix="", tags=["transaction_status"]
)
# api_router.include_router(last_transaction.router, prefix="", tags=["last_transaction"])
