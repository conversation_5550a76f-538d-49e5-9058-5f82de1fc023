from typing import Any
from core.settings import settings
from core.config import (
    authorization,
    redis,
    eia_client,
    catalog_client,
    ngpg_client,
    dwallet_client,
)
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, Header
from fastapi.responses import JSONResponse
from ngmi_logging.utils import get_correlation_id, add_params_into_access_log
from schemas import PaymentInitiate
from broker.helpers import publish_payment_msg, publish_purchase_successful_msg
from swagger.payment import initiate_sample_response
from utils import is_valid_source

router = APIRouter()


http_protocol = "http"


@router.post(
    "/initiate",
    responses=initiate_sample_response,
)
async def initiate(
    payment: PaymentInitiate,
    request: Request,
    background_tasks: BackgroundTasks,
    source: str = Header(default=""),
    profile: dict = Depends(authorization),
    x_authorization_extra: str = Header(None),
) -> Any:
    """Initiates payment procedure on NGPG.
    - **service**: Defines NGPG service type(ex:NormalBolton,DirectTopup,etc.)
    - **amount**: Defines payment amount
    - **reference_id**: Defines payment reference ID
    - **order_id**: Defines payment order ID
    - **payment_mode_id**: Defines selected payment mode ID
    - **callback_url**: Defines callback url which will be redirected into
    - **bank_id**: Defines selected bank ID
    - **auto_renew**: Defines if offer has to be renewed or not

    Raises:

        401: If JWT token is invalid
        400: If bank ID is missing for BA payment mode(bank payment)

    Returns:

        transaction_id: Represents NGPG transaction ID
        response_time: Represents response timestamp
        command_status: Represents NGPG transaction command status
        response_msg: Represents NGPG transaction response message
        order_id: Represents NGPG order ID
        reference_id: Represents NGPG reference ID
        redirection_url: Represents url redirection for payment process
    """
    phone_number = profile["phone_number"]
    language = profile["language"]
    sim_type = profile["sim_type"]

    payment = payment.model_dump()
    service = payment["service"]
    amount = payment["amount"]
    offer_code = payment["offer_code"]
    reference_id = payment["reference_id"]
    order_id = payment["order_id"]
    payment_mode_id = payment["payment_mode_id"]
    callback_type = payment["callback_type"]
    bank_id = payment["bank_id"]
    option_id = str(payment["option_id"])
    scheme_id = payment["scheme_id"]
    auto_renew = payment["auto_renew"]

    correlation_id = get_correlation_id()

    if not is_valid_source(source):
        return JSONResponse(
            status_code=422,
            content={
                "type": "https://my.irancell.ir/errors/",
                "title": f"Invalid source in header- {source}",
            },
        )
    add_params_into_access_log(key="source", value=source)

    add_params_into_access_log(key="amount", value=str(amount))
    add_params_into_access_log(key="target", value=str(service))
    add_params_into_access_log(key="internal_ref_id", value=str(reference_id))

    if service == "WalletCashIn":
        if not x_authorization_extra:
            raise HTTPException(status_code=412, detail="Wallet token is required.")

        wallet_profile = await dwallet_client.validate_wallet_token(
            phone_number=phone_number, wallet_token=x_authorization_extra
        )

        if not wallet_profile:
            raise HTTPException(status_code=412, detail="Invalid Wallet token.")

        elif phone_number != wallet_profile["phone_number"]:
            raise HTTPException(status_code=400, detail="Invalid Wallet token.")

    if service in ["OnlineBuyable", "BuyyableOffer"]:
        lock = await redis.lock_acquire(
            key=f"lock_{phone_number}-{offer_code}",
            expiry=int(settings.RACE_CONDITION_TIME_LIMIT),
        )
        if not lock:
            raise HTTPException(status_code=400, detail="too many requests.")

    base_url = request.url._url.replace(request.url.path, "").replace(
        f"{http_protocol}://", "https://"
    )
    if callback_type == "native":
        callback_url = f"{base_url}/api/payment/v1/result/landing/{language}"
    else:
        callback_url = f"{base_url}/api/payment/v1/result/app/{language}"

    channel_name = (
        settings.NGPG_CHANNEL_APP
        if profile["client_name"] == "application"
        else settings.NGPG_CHANNEL_WEB
    )

    if service == "FTTHBolton":
        if sim_type == "fttx":
            customer_profile = await eia_client.fftx_get_subscriber_details(
                fttx_id=phone_number,
                fields=["notification_phone_number"],
            )
            phone_number = customer_profile["notification_phone_number"]
        else:
            raise HTTPException(
                status_code=400,
                detail="This service is only available for fttx subscribers",
            )

    ngpg_response = await ngpg_client.capture_payment(
        phone_number=phone_number,
        service=service,
        amount=amount,
        channel_name=channel_name,
        order_id=order_id,
        reference_id=reference_id,
        payment_mode_id=payment_mode_id,
        callback_url=callback_url,
        offer_code=offer_code,
        language=language,
        bank_id=bank_id,
        option_id=option_id,
        scheme_id=scheme_id,
        auto_renew=auto_renew,
    )

    successful_purchase = False
    result_code = ngpg_response["resultCode"]
    result_message = ngpg_response["responseMsg"].lower()

    if (
        not payment_mode_id.lower() == "ba"
        and result_code == "0"
        and result_message == "success"
    ):
        successful_purchase = True

    if result_code in ["11113", "149"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/pretopost/validation_error",
                "title": "request for pre-to-post upgrade failed due to validation",
                "detail": "Shared service is active",
            },
        )

    if result_code == "150":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/payment/pretopost/activation_error",
                "title": "request for pre-to-post upgrade failed due to activation",
                "detail": "MSISDN is not active",
            },
        )
    elif not result_code == "0":
        raise HTTPException(status_code=400, detail=ngpg_response["responseMsg"])

    if successful_purchase:
        background_tasks.add_task(
            publish_purchase_successful_msg,
            msisdn=phone_number,
            ngpg_service=payment["service"],
            method="CapturePayment",
            amount=payment["amount"],
            reference_id=reference_id,
            correlation_id=correlation_id,
        )

    if result_message == "success":
        catalog_response = await catalog_client.get_offer_details(
            phone_number, offer_code
        )
        if not catalog_response["error_status"]:
            offer_details = catalog_response["offer_details"]
            background_tasks.add_task(
                publish_payment_msg,
                service=service,
                channel=profile["client_name"],
                phone_number=phone_number,
                registration_date=profile.get("mtni", {"registration_date": None})[
                    "registration_date"
                ],
                amount=amount,
                offer_code=offer_code,
                offer_type=offer_details["offer_type"],
                offer_sim_type=profile["sim_type"],
                offer_customer_type=profile["customer_type"],
                correlation_id=correlation_id,
                source="trigger" if source == "trigger" else "",
            )

    res = {
        "transaction_id": ngpg_response["transactionId"],
        "response_time": ngpg_response["responseTime"],
        "command_status": ngpg_response["commandStatus"],
        "response_msg": ngpg_response["responseMsg"],
        "order_id": ngpg_response["orderId"],
        "reference_id": ngpg_response["referenceId"],
        "redirection_url": ngpg_response["redirectionURL"],
    }
    if bank_id:
        res["bank_id"] = bank_id

    return res
