from typing import Any
from core.config import authorization, notification_client
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse

router = APIRouter()


@router.post("/otp/request")
async def request_replace_package_otp(
    profile: dict = Depends(authorization),
) -> Any:
    """Request for otp with intention of replace_package
    Raises:
        400: too many otp request
    Returns:
        None
    """

    phone_number = profile["phone_number"]
    sim_type = profile["sim_type"]
    client_id = profile["client_id"]
    language = profile["language"]

    if not sim_type == "fttx":
        raise HTTPException(status_code=400, detail="Invalid operation")

    otp_response = await notification_client.send_sms_otp(
        intention="replace_package",
        phone_number=phone_number,
        client_id=client_id,
        language=language,
    )

    if otp_response["error_status"]:
        return JSONResponse(status_code=400, content=otp_response.get("data"))
    else:
        return {"phone_number": otp_response["recipient"]}
