from datetime import datetime
from ..publisher import rabbitmq_publisher
from ngmi_logging.utils import process_time

@process_time()
async def publish_payment_msg(
    service,
    channel,
    phone_number,
    registration_date,
    amount,
    source,
    offer_code=None,
    offer_type=None,
    offer_sim_type=None,
    offer_customer_type=None,
    correlation_id="",
):
    msg = {
        "event": "ngmi_purchase",
        "at": datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
        "service": str(service),
        "channel": str(channel),
        "msisdn": str(phone_number),
        "registration_date": str(registration_date),
        "amount": str(amount),
        "source": str(source),
        "offer_code": str(offer_code),
        "offer_type": str(offer_type),
        "offer_sim_type": str(offer_sim_type),
        "offer_customer_type": str(offer_customer_type),
        "correlation_id": str(correlation_id),
    }

    await rabbitmq_publisher.publish("ngmi.purchase", msg)
