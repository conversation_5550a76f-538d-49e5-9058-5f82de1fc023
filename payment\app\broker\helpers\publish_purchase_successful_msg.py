from datetime import datetime
import pytz
from ..publisher import rabbitmq_publisher
from ngmi_logging.utils import process_time


@process_time()
async def publish_purchase_successful_msg(
    msisdn,
    ngpg_service,
    method,
    amount,
    reference_id,
    correlation_id="",
):
    msg = {
        "event": "ngmi_purchase_successful",
        "at": datetime.now(pytz.timezone("Asia/Tehran")).isoformat(),
        "msisdn": str(msisdn),
        "ngpg_service": str(ngpg_service),
        "method": str(method),
        "amount": str(amount),
        "reference_id": str(reference_id),
        "correlation_id": str(correlation_id),
    }

    await rabbitmq_publisher.publish("ngmi.purchase_successful", msg)
