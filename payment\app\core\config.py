from .settings import settings
import json
from ngmi_logging import Logger
from ngmi_authorization import AuthorizationJWT
from ngmi_redis import RedisClient
from ngmi_notification import NotificationClient
from ngmi_eia import EIAClient
from third_parties import NGPGClient, CatalogClient, ISSClient, DWalletClient


logger = Logger(
    url=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG}",
    aurl=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG2}",
    service_name=settings.SERVICE_NAME,
    worker_id=settings.WORKER_ID,
)

redis = RedisClient(
    nodes=json.loads(settings.REDIS_CLUSTER_NODES),
    username=settings.REDIS_CLUSTER_USERNAME,
    password=settings.REDIS_CLUSTER_PASSWORD,
    logger=logger,
    prefix=settings.REDIS_PREFIX,
)

authorization = AuthorizationJWT(
    jwt_public_key=settings.JWT_PUBLIC_KEY,
    validate_refresh_token="redis_blacklist",
    redis_client=redis,
    redis_prefix=settings.REDIS_PREFIX,
)

eia_client = EIAClient(
    endpoint=settings.EIA_ENDPOINT,
    username=settings.EIA_USERNAME,
    password=settings.EIA_PASSWORD,
    logger=logger,
)

notification_client = NotificationClient(
    base_url=settings.NOTIFICATION_SERVICE_ENDPOINT,
    eia_client=eia_client,
    logger=logger,
)

ngpg_client = NGPGClient(
    base_url=settings.NGPG_ENDPOINT,
    token=settings.NGPG_TOKEN,
    mid=settings.NGPG_MID,
    logger=logger,
)

catalog_client = CatalogClient(
    base_url=settings.CATALOG_SERVICE_ENDPOINT,
    logger=logger,
)

dwallet_client = DWalletClient(
    base_url=settings.DIGITAL_WALLET_SERVICE_ENDPOINT,
    logger=logger,
)

iss_client = ISSClient(
    base_url=settings.ISS_ENDPOINT,
    username=settings.ISS_USERNAME,
    password=settings.ISS_PASSWORD,
    api_key=settings.ISS_API_KEY,
    tenant=settings.ISS_TENANCY_NAME,
    logger=logger,
)
