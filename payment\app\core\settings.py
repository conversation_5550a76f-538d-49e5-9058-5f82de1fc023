import os
import secrets
from typing import Optional
from files.parse_catalog_offers import parse_catalog_offers
from pydantic_settings import BaseSettings
from pydantic import HttpUrl
from uuid import uuid4

parent_dir = os.path.abspath(os.path.abspath(os.path.dirname(__file__)) + "/../")


class Settings(BaseSettings):
    API_V1_STR: str = "/v1"
    API_V2_STR: str = "/v2"
    API_V3_STR: str = "/v3"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    SERVICE_NAME: Optional[str] = "payment"
    ENVIRONMENT: Optional[str] = "development"

    JWT_PUBLIC_KEY: str = open(f"{parent_dir}/rsa/public.pem", "r").read()
    JWT_PRIVATE_KEY: str = open(f"{parent_dir}/rsa/private.key", "r").read()
    NGPG_PRIVATE_KEY : str= open(f"{parent_dir}/rsa/IPSHostPrivate.pem", "r").read()

    WORKER_ID :str = uuid4().hex

    CATALOG_OFFERS :dict= parse_catalog_offers()

    REDIS_PREFIX: str

    FLUENTD_HTTP_ENDPOINT: str
    FLUENTD_HTTP_PORT: str
    FLUENTD_HTTP_TAG: str
    FLUENTD_HTTP_TAG2: Optional[str] = "http-ngmi-additional"
    FLUENTD_HTTP_PROTOCOL: str = "http"

    RACE_CONDITION_TIME_LIMIT: str = "120"

    NGPG_TOKEN: str
    NGPG_ENDPOINT: str
    NGPG_MID: str
    NGPG_CHANNEL_APP: str
    NGPG_CHANNEL_WEB: str

    CALLBACK_URL_NATIVE: str
    CALLBACK_URL_WEB: str

    EIA_ENDPOINT: str
    EIA_USERNAME: str
    EIA_PASSWORD: str

    ISS_ENDPOINT: str
    ISS_USERNAME: str
    ISS_PASSWORD: str
    ISS_TENANCY_NAME: str
    ISS_API_KEY: str
    ISS_CHANNEL_NAME: str

    SENTRY_DSN: HttpUrl

    MODERATELY_PROTECTED_URLS: list = []

    SENTRY_TRACES_SAMPLE_RATE: str = "0.01"

    REDIS_CLUSTER_NODES: str
    REDIS_CLUSTER_USERNAME: str
    REDIS_CLUSTER_PASSWORD: str

    AUTHORIZATION_REDIS_PREFIX: str
    CATALOG_REDIS_PREFIX: str

    CATALOG_SERVICE_ENDPOINT: str

    DIGITAL_WALLET_SERVICE_ENDPOINT: str

    NOTIFICATION_SERVICE_ENDPOINT: str

    RABBITMQ_URL: str

    INITIATE_SOURCES: str = '["repeatLastTranction", "favorite_transactions", "trigger"]'
    
    DAILY_GIFT_BOLTON_RATE_LIMIT: int

    class Config:
        case_sensitive = True


settings = Settings()
