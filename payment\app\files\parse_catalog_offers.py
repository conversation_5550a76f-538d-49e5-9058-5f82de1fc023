import csv
import os


def dict_converter(i):
    if not i:
        return ""
    res = {}
    try:
        for j in i.split("++"):
            k, v = j.split("::")
            res[k.strip()] = v.strip()
    except ValueError:
        for combo in i.split("///"):
            combo = combo.strip().replace("(", "---").split("---")
            country = combo[0].strip()
            operators = combo[1].replace("(", "").replace(")", "").split(",")
            if not country in res.keys():
                res[country] = []
            for op in operators:
                if not op in res[country]:
                    res[country].append(op.strip())
    return res


def parse_catalog_offers():
    absolute_path = os.path.dirname(os.path.abspath(__file__))
    rows = csv.DictReader((open(f"{absolute_path}/catalog.csv", encoding="utf-8")))

    offers = []

    for row in rows:
        if row.get("active") == "no":
            continue
        offer_type = row["type"]

        offer_type = offer_type.lower()
        if offer_type == "internet":
            offer_type = "data"
        elif offer_type == "digital_wallet":
            offer_type = "dw"
        elif offer_type == "wowrecharge":
            offer_type = "wow_recharge"

        i = {
            "category": row["category"].lower(),
            "sub_type": row["customer_type"].lower(),
            "sim_type": row["sim_type"].lower(),
            "profile_type": row["account_type"].lower(),
            "offer_type": offer_type,
            "fa_title": row["fa_title"],
            "en_title": row["en_title"],
            "fa_description": row["fa_description"],
            "en_description": row["en_description"],
            "fa_details": dict_converter(row["fa_details"]),
            "en_details": dict_converter(row["en_details"]),
            "upc_code": row["upc_code"],
            "duration": row["duration"],
            "duration_unit": row["duration_unit"].lower(),
            "price": int(row["price"].replace(",", "")),
            "auto_renewal": row["auto_renewal"],
            "giftable_code": row["giftable_code"],
            "giftable": True if row["giftable"] == "yes" else False,
            "en_tags": list(map(str.strip, row["en_tags"].split(",")))
            if row["en_tags"]
            else [],
            "fa_tags": list(map(str.strip, row["fa_tags"].split(",")))
            if row["fa_tags"]
            else [],
        }

        offers.append(i)

    res = {}
    for o in offers:
        res[o["upc_code"]] = o
    return res
