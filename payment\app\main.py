import sentry_sdk
from api.api_v1.api import api_router as api_router_v1
from api.api_v2.api import api_router_v2
from api.api_v3.api import api_router_v3
from core.settings import settings
from core.config import logger, redis
from broker.publisher import rabbitmq_publisher
from fastapi import FastAPI
from health import live, ready
from ngmi_logging import LoggingMiddleware
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
from sentry_sdk.integrations.httpx import HttpxIntegration
from starlette.middleware.errors import ServerErrorMiddleware
from httpx import ConnectError, ConnectTimeout, ReadError, ReadTimeout
from ngmi_http import InvalidUpstreamResponse
from swagger import servers
from handlers.exception_handler import (
    global_execution_handler,
    invalid_upstream_response_exception_handler,
    httpx_read_error_exception_handler,
    httpx_read_timeout_exception_handler,
    httpx_connect_timeout_exception_handler,
    httpx_connect_error_exception_handler,
)

from contextlib import asynccontextmanager



@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.setup()
    await redis.connect()
    await rabbitmq_publisher.start()
    await logger.info("startup")
    yield
    await redis.disconnect()
    await rabbitmq_publisher.stop()
    await logger.info("shutdown")


app = FastAPI(
    title=settings.SERVICE_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    servers=servers.SERVERS,
    lifespan=lifespan,
)

app.add_middleware(
    ServerErrorMiddleware,
    handler=global_execution_handler,
)
app.add_middleware(
    LoggingMiddleware,
    logger=logger,
)

app.include_router(api_router_v1, prefix=settings.API_V1_STR)
app.include_router(api_router_v2, prefix=settings.API_V2_STR)
app.include_router(api_router_v3, prefix=settings.API_V3_STR)
app.add_api_route("/v1/live", live)
app.add_api_route("/v1/ready", ready)

app.add_exception_handler(
    InvalidUpstreamResponse, invalid_upstream_response_exception_handler
)
app.add_exception_handler(ReadError, httpx_read_error_exception_handler)
app.add_exception_handler(ReadTimeout, httpx_read_timeout_exception_handler)
app.add_exception_handler(ConnectTimeout, httpx_connect_timeout_exception_handler)
app.add_exception_handler(ConnectError, httpx_connect_error_exception_handler)


sentry_sdk.utils.MAX_STRING_LENGTH = 9999999999999999999999999999999999
sentry_sdk.init(
    dsn=settings.SENTRY_DSN,
    environment=settings.ENVIRONMENT,
    integrations=[HttpxIntegration()],
    traces_sample_rate=float(settings.SENTRY_TRACES_SAMPLE_RATE),
)
app = SentryAsgiMiddleware(app)
