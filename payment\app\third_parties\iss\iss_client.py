from ngmi_http import HTTPBase, InvalidUpstreamResponse
from httpx import AsyncClient
from datetime import datetime
from .parser import iss_parser


class ISSClient(HTTPBase):
    def __init__(
        self, base_url, username, password, api_key, tenant, logger, http_client=None
    ) -> None:
        if not http_client:
            http_client = AsyncClient(verify=False)
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.username = username
        self.password = password
        self.api_key = api_key
        self.tenant = tenant
        self._token = None

    async def _call_api(
        self,
        path,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        timeout=None,
        request_parser=None,
    ):
        url = self.base_url + path

        request_params = {
            "url": url,
            "headers": headers if headers else {},
            "method": method,
            "timeout": timeout,
            "api_name": api_name,
            "subject": subject,
            "service": "ISS",
            "request_parser": request_parser
        }

        if body:
            request_params["body"] = body
        if params:
            request_params["params"] = params
        if not request_parser:
            request_params["request_parser"] = iss_parser

        response = await self._call_rest_api(**request_params)
        if response["status_code"] == 401:
            self._token = None
            request_params["headers"]["authorization"] = (
                "Bearer " + await self.token(),
            )
            response = await self._call_rest_api(**request_params)

        return response

    async def token(self):
        if self._token is None:

            body = {
                "username": self.username,
                "password": self.password,
                "tenancyName": self.tenant,
            }

            response = await self._call_api(
                path="/api/account/authenticate",
                method="POST",
                api_name="Login",
                body=body,
                subject="************",
            )

            try:
                self._token = response["data"]["result"]
            except:
                raise InvalidUpstreamResponse

        return self._token

    async def create_dedicated_survey(self, phone_number, channel_name, custom_fields):

        headers = {
            "authorization": "Bearer " + await self.token(),
        }

        body = {
            "collectorApiKey": self.api_key,
            "customerEvent": {
                "customer": {"msisdn": phone_number, "contactNumber": phone_number},
                "event": {
                    "interactionDate": datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
                    "channelName": channel_name,
                    "eventDetails": "After Bolton Purchase",
                    "customField_Title_1": "BoltonType",
                    "customField_Value_1": custom_fields["bolton_type"],
                    "customField_Title_2": "SIMType",
                    "customField_Value_2": custom_fields["sim_type"],
                    "customField_Title_3": "Price",
                    "customField_Value_3": custom_fields["price"],
                    "customField_Title_4": "BoltonName",
                    "customField_Value_4": custom_fields["bolton_name"],
                    "customField_Title_5": "SubType",
                    "customField_Value_5": custom_fields["sub_type"],
                },
            },
        }

        response = await self._call_api(
            path="/api/event/createDedicatedSurvey",
            method="POST",
            api_name="create_dedicated_survey",
            subject=phone_number,
            headers=headers,
            body=body,
        )

        final_res = {"error_status": False}

        try:
            jresp = response["data"]
        except:
            final_res["error_status"] = True
            return final_res

        if jresp["success"]:
            final_res["data"] = jresp["result"]["fullUrl"]
        else:
            final_res["error_status"] = True

        return final_res
