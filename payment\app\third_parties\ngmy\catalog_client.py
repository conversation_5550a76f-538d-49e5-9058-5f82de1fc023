from ngmi_http import HTTPBase
from ngmi_logging.context import correlation_id
from httpx import AsyncClient, Timeout
from .parser import ngmy_parser

class CatalogClient(HTTPBase):
    def __init__(self, base_url, logger, http_client=None) -> None:
        if not http_client:
            http_client = AsyncClient(timeout=Timeout(20))
        super().__init__(logger, http_client)
        self.base_url = base_url

    async def _call_api(
        self,
        path,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        timeout=None,
        request_parser=None
    ):

        url = self.base_url + path
        if x_correlation_id := correlation_id.get():
            if not headers:
                headers = {}
            headers["x-correlation-id"] = x_correlation_id
            
        if not request_parser:
            request_parser = ngmy_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            timeout=timeout,
            api_name=api_name,
            subject=subject,
            service="catalog",
            is_internal=True,
            request_parser=request_parser
        )

        return response

    async def get_offer_details(self, phone_number, offer_code):
        res = {"error_status": False, "offer_details": None}
        response = await self._call_api(
            path=f"/v2/offers/private_offer_details?offer_code={offer_code}",
            method="GET",
            api_name="private_offer_details",
            subject=phone_number,
        )
        if response["status_code"] != 200:
            res["error_status"] = True
        else:
            res["offer_details"] = response["data"]

        return res
