from ngmi_http import HTTPBase
from ngmi_logging.context import correlation_id
from httpx import AsyncClient, Timeout
from .parser import ngmy_parser

class DWalletClient(HTTPBase):
    def __init__(self, base_url, logger, http_client=None) -> None:
        if not http_client:
            http_client = AsyncClient(timeout=Timeout(20))
        super().__init__(logger, http_client)
        self.base_url = base_url

    async def _call_api(
        self,
        path,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        timeout=None,
        request_parser=None
    ):

        url = self.base_url + path
        
        if not request_parser:
            request_parser=ngmy_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            timeout=timeout,
            api_name=api_name,
            subject=subject,
            service="digital-wallet",
            is_internal=True,
            request_parser=request_parser
        )

        return response

    async def validate_wallet_token(self, phone_number, wallet_token):
        headers = {"x-authorization-extra": wallet_token}
        response = await self._call_api(
            path="/v1/private/validate_token",
            method="POST",
            headers=headers,
            api_name="dwallet_validate_token",
            subject=phone_number,
        )

        if response["status_code"] == 200:
            return response["data"]

        return False
