from ngmi_http import HTTPBase, InvalidUpstreamResponse
from uuid import uuid4
from datetime import datetime
from .ngpg_services import services
from .parser import ngpg_parser


class NGPGClient(HTTPBase):
    def __init__(self, base_url, token, mid, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.token = token
        self.mid = mid

    @staticmethod
    def get_valid_msisdn(phone_number):
        if len(phone_number) == 10 and phone_number.startswith("9"):
            return f"98{phone_number}"
        elif len(phone_number) == 11 and phone_number.startswith("0"):
            return f"98{phone_number[1:]}"
        elif len(phone_number) == 12 and phone_number.startswith("989"):
            return phone_number
        else:
            raise ValueError("Invalid msisdn")

    async def _call_api(
        self,
        path,
        method,
        api_name,
        subject,
        body=None,
        params=None,
        headers=None,
        timeout=None,
        request_parser=None,
    ):

        url = self.base_url + path

        headers = headers or {}

        headers.update(
            {
                "Authentication": self.token,
                "X-MID": self.mid,
                "Accept": "application/json",
            }
        )

        if not request_parser:
            request_parser = ngpg_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            timeout=timeout,
            api_name=api_name,
            subject=subject,
            service="NGPG",
            request_parser=request_parser,
        )

        return response

    async def get_payment_modes(
        self,
        phone_number,
        service: str,
        amount: int,
        channel_name: str,
        offer_code: str = "",
        language: str = "fa",
        beneficiary_phone_number: str = None,
        recipients_list: list = None,
        data_counter: str = None,
        voice_counter: str = None,
        sms_counter: str = None,
        validity_duration: str = None,
        increased_cl_amount: int = None,
        fttx_id: str = None,
        offer_desc: str = None,
        installment_details=None,
        # sdk_version: str = None,
    ):

        if service not in services:
            raise ValueError(f"Invalid NGPG Service {service}")

        path = "/getPaymentMode"
        body = {
            "channel": channel_name,
            "orderId": uuid4().hex,
            "requestTime": datetime.now().strftime("%d%m%Y %H:%M:%S:%f"),
            "mobileNumber": self.get_valid_msisdn(phone_number),
            "mid": self.mid,
            "amount": amount,
            "service": service,
            "offerCode": offer_code,
            "parameters": {
                "parameter": [{"key": "is_bank_list_required", "value": True}]
            },
        }
        subject = phone_number

        if service != "SharedAccConsumerAdd" and beneficiary_phone_number:
            body["parameters"]["parameter"].append(
                {
                    "key": "BMobileNumber",
                    "value": self.get_valid_msisdn(beneficiary_phone_number),
                }
            )

        if service in [
            "NormalBolton",
            "GiftBolton",
            "DTSOffer",
            "OnlineBuyable",
            "BuyyableOffer",
            "SharedBolton",
        ]:
            if not offer_desc:
                raise ValueError("Insufficient parameters")

            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )

        elif service == "GiftBoltonMulti":
            if not recipients_list:
                raise ValueError("Insufficient parameters")
            body["recipeintList"] = {"items": []}
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )
            body["recipeintList"]["items"] = recipients_list

        elif service == "FTTHBolton":
            if not offer_desc or not fttx_id:
                raise ValueError("Insufficient parameters")

            subject = fttx_id
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )
            body["parameters"]["parameter"].append({"key": "ftth_id", "value": fttx_id})
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )
            body["parameters"]["parameter"].append(
                {"key": "changeOfferFlag", "value": "True"}
            )
            body["parameters"]["parameter"].append(
                {
                    "key": "InitialOfferFlag",
                    "value": "False",
                }
            )

        elif service in ["DynamicBolton", "MPDynamicBolton"]:
            if not (data_counter or sms_counter or voice_counter or validity_duration):
                raise ValueError("Insufficient parameters")
            body["parameters"]["parameter"].append(
                {"key": "data_counter", "value": data_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "sms_counter", "value": sms_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "voice_counter", "value": voice_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "no_of_days", "value": validity_duration}
            )

        elif service == "SharedAccConsumerAdd":
            if not beneficiary_phone_number:
                raise ValueError("Insufficient parameters")

            offer_descs = {"fa": "افزودن زیرشاخه", "en": "adding consumer"}
            body["parameters"]["parameter"].append(
                {"key": "providerMSISDN", "value": self.get_valid_msisdn(phone_number)}
            )
            body["parameters"]["parameter"].append(
                {
                    "key": "consumerMSISDN",
                    "value": self.get_valid_msisdn(beneficiary_phone_number),
                }
            )
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_descs[language]}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )

        elif service == "Pre2PostMigration":
            if increased_cl_amount is None:
                raise ValueError("Insufficient parameters")

            body["parameters"]["parameter"].append(
                {"key": "increased_amount", "value": increased_cl_amount}
            )
            body["parameters"]["parameter"].append({"key": "paymentType", "value": "F"})

        elif service == "WalletCashIn":
            offer_desc = "شارژ " + str(amount) + "ریالی"
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append({"key": "languageId", "value": "F"})
        elif service == "BNPLPayBack":
            body["installmentDetails"] = installment_details

        response = await self._call_api(
            path=path,
            method="POST",
            body=body,
            api_name="getPaymentMode",
            subject=subject,
        )

        if response["status_code"] == 200:
            try:
                data = response["data"]
                return data
            except:
                raise InvalidUpstreamResponse
        else:
            raise InvalidUpstreamResponse

    async def capture_payment(
        self,
        phone_number,
        service: str,
        amount: int,
        channel_name: str,
        order_id: str,
        reference_id: str,
        payment_mode_id: str,
        callback_url: str,
        offer_code: str,
        language: str = "fa",
        bank_id: str = None,
        option_id: str = None,
        scheme_id: str = None,
        auto_renew: bool = False,
    ):

        if service not in services:
            raise ValueError(f"Invalid NGPG Service {service}")

        path = "/capturePayment"
        body = {
            "channel": channel_name,
            "orderId": order_id,
            "requestTime": datetime.now().strftime("%d%m%Y %H:%M:%S:%f"),
            "mobileNumber": self.get_valid_msisdn(phone_number),
            "amount": amount,
            "requestType": "NSEAM",
            "referenceId": reference_id,
            "loyaltyPointtoRedeem": "",
            "mid": self.mid,
            "paymentModeId": payment_mode_id,
            "service": service,
            "offerCode": offer_code,
            "callBackUrl": callback_url,
            "languageId": "F" if language == "fa" else "E",
            "parameters": {"parameter": []},
        }
        subject = phone_number

        if payment_mode_id == "BA":
            body["parameters"] = {"parameter": [{"key": "bankId", "value": bank_id}]}
        elif payment_mode_id == "PP":
            body["parameters"] = {
                "parameter": [
                    {"key": "optionId", "value": option_id},
                    {"key": "schemeId", "value": scheme_id},
                    {"key": "bankId", "value": bank_id},
                ]
            }
        elif payment_mode_id == "BNPLO":
            body["parameters"] = {"parameter": [{"key": "schemeId", "value": "1"}]}
        if auto_renew:
            body["parameters"]["parameter"].append(
                {"key": "autoRenewFlag", "value": "Y"}
            )

        response = await self._call_api(
            path=path,
            method="POST",
            body=body,
            api_name="capturePayment",
            subject=subject,
        )
        if response["status_code"] == 200:
            try:
                data = response["data"]
                return data
            except:
                raise InvalidUpstreamResponse
        else:
            raise InvalidUpstreamResponse

    async def transaction_history_token(self, token: str, ngpg_channel: str):
        path = "/transactionHistoryByToken"
        params = {"token": token}
        headers = {"X-CHANNEL": ngpg_channel, "Content-Type": "application/json"}

        response = await self._call_api(
            path=path,
            method="GET",
            headers=headers,
            params=params,
            api_name="transactionHistoryByToken",
            subject=token,
        )
        if response["status_code"] == 200:
            try:
                data = response["data"]
                return data
            except:
                raise InvalidUpstreamResponse
        else:
            raise InvalidUpstreamResponse
