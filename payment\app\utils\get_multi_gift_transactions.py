from datetime import datetime, timedelta
from typing import Optional
import httpx
from core.config import settings
from fastapi import HTTPException
from ngmy_logging.logger import aiointegration_exception_logger, aiointegration_logger
from utils.ngpg_code_map import ngpg_code_map
from schemas.multi_gift_transaction import TransactionDetail, MultiGiftTransactionResponse


async def get_multi_gift_transactions(
    phone_number: str,
    cow_date: Optional[str] = None,
    channel: str = "application",
    sim_type: str = "fd",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status_filter: Optional[str] = None,
    limit: int = 100
) -> MultiGiftTransactionResponse:

    headers = {
        "Authentication": settings.NGPG_TOKEN,
        "X-MID": settings.NGPG_MID,
        "Content-Type": "application/json",
        "Accept": "application/json",
    }

    today = datetime.now()
    
    ngpg_channel = settings.NGPG_CHANNEL_APP
    if channel == "web":
        ngpg_channel = settings.NGPG_CHANNEL_WEB

    # Set date range
    if start_date:
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    else:
        start_datetime = today - timedelta(days=30)
        if cow_date:
            cow_datetime = datetime.strptime(cow_date, "%Y-%m-%d")
            if cow_datetime > start_datetime:
                start_datetime = (
                    cow_datetime + timedelta(days=1)
                    if cow_datetime.day <= today.day
                    else cow_datetime
                )

    if end_date:
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    else:
        end_datetime = today

    if start_datetime > end_datetime:
        return MultiGiftTransactionResponse(
            total_transactions=0,
            successful_transactions=0,
            failed_transactions=0,
            pending_transactions=0,
            transactions=[]
        )

    body = {
        "limit": limit,
        "startDate": start_datetime.strftime("%Y-%m-%d"),
        "msisdn": phone_number[2:],  
        "endDate": end_datetime.strftime("%Y-%m-%d"),
        "channel": ngpg_channel,
        "service": "MultiGiftBolton" 
    }

    if sim_type == "fttx":
        body["service"] = "FTTHBolton"

    url = f"{settings.NGPG_ENDPOINT}/transactionHistory"

    async with httpx.AsyncClient() as client:
        try:
            resp = await client.post(url, json=body, headers=headers)
            jresp = resp.json()
        except Exception as e:
            await aiointegration_exception_logger(
                event="upstream.NGPG.transactionHistory",
                api_name="transactionHistory",
                service="NGPG",
                subject=phone_number,
                url=url,
                method="post",
                content=body,
                header=headers,
                exception=e,
            )
            raise

    await aiointegration_logger(
        event="upstream.NGPG.transactionHistory",
        api_name="transactionHistory",
        service="NGPG",
        subject=phone_number,
        response=resp,
    )

    if not resp.status_code == 200 or not jresp["resultCode"] == "0":
        await aiointegration_exception_logger(
            event="upstream.NGPG.transactionHistory",
            api_name="transactionHistory",
            service="NGPG",
            subject=phone_number,
            url=url,
            method="post",
            content=body,
            header=headers,
            exception=ngpg_code_map.get(jresp["resultCode"], "unknown result code"),
        )
        raise HTTPException(status_code=400, detail=resp.json()["responseMsg"])

    transactions = []
    successful_count = 0
    failed_count = 0
    pending_count = 0

    if jresp.get("transactionList"):
        sorted_transactions = sorted(
            jresp["transactionList"], 
            key=lambda x: x.get("transactionDate", ""), 
            reverse=True
        )
        
        for transaction in sorted_transactions:
            if transaction.get("service") not in ["MultiGiftBolton", "GiftBolton"]:
                continue
                
            # Apply status filter if specified
            if status_filter and transaction.get("status", "").upper() != status_filter.upper():
                continue
            
            status = transaction.get("status", "UNKNOWN").upper()
            if status in ["SUCCESS", "SUCCESSFUL", "COMPLETED"]:
                status = "SUCCESS"
                successful_count += 1
            elif status in ["FAILED", "FAILURE", "ERROR"]:
                status = "FAILED"
                failed_count += 1
            elif status in ["PENDING", "PROCESSING", "IN_PROGRESS"]:
                status = "PENDING"
                pending_count += 1
            else:
                status = "FAILED"
                failed_count += 1
            
            transaction_detail = TransactionDetail(
                transaction_id=transaction.get("transactionId", ""),
                order_id=transaction.get("orderId", ""),
                reference_id=transaction.get("referenceId", ""),
                service=transaction.get("service", ""),
                amount=float(transaction.get("amount", 0)),
                paid_amount=float(transaction.get("paidAmount", 0)) if transaction.get("paidAmount") else None,
                offer_code=transaction.get("offerCode"),
                beneficiary_phone_number=transaction.get("serviceMsisdn"),
                transaction_date=transaction.get("transactionDate", ""),
                status=status,
                status_description=transaction.get("statusDesc"),
                payment_mode=transaction.get("paymentModeDesc"),
                bank_name=transaction.get("bankName"),
                bank_reference_id=transaction.get("bankReferenceId"),
                channel=transaction.get("channel")
            )
            
            transactions.append(transaction_detail)

    return MultiGiftTransactionResponse(
        total_transactions=len(transactions),
        successful_transactions=successful_count,
        failed_transactions=failed_count,
        pending_transactions=pending_count,
        transactions=transactions
    )
