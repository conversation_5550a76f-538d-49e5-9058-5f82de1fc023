ngpg_code_map = {
    "0": "Success",
    "1": "Service not available",
    "3": "Invalid message format – Json error",
    "4": "Invalid Parameter – json parameter Error",
    "100": "Internal system error",
    "101": "Invalid payload API error",
    "102": "Invalid channel Id",
    "103": "Invalid merchant Id",
    "104": "Invalid Service parameter in API",
    "105": "Invalid mobile number in API",
    "106": "Invalid amount in API",
    "107": "Invalid feature in API",
    "108": "Invalid order Id in API",
    "109": "Invalid username in API",
    "110": "Invalid password in API",
    "111": "Merchant ID disabled",
    "113": "No payment modes are available",
    "114": "Invalid offer code",
    "115": "Get balance error from IN",
    "117": "Transaction not found",
    "118": "Invalid payment status- capturePayment",
    "119": "Invalid transaction amount- capturePayment",
    "120": "Duplicate transaction capturepayment (Duplicate Ref id)",
    "121": "Inactive service – GetpaymentMode",
    "123": "Invalid payment mode",
    "124": "Invalid transaction date",
    "125": "Invalid reference number",
    "134": "Service Not allowed this channel",
    "135": "Duplicate Order ID",
    "136": "CapturePayment Transaction failure",
    "137": "Invalid service / Inactive service – Capture payment",
    "153": "Default IN error",
    "161": "Failure response from Loyalty System for redeem loyalty points 164 Reference ID expired",
    "175": "Failure response from Diameter - MA deduction",
    "181": "INVALID_BANK_RESTRICTION_AMOUNT (Higher Amount)",
    "191": "Service Activation read time out",
    "192": "Service Activation connection time out",
    "203": "User cancelled the bank transaction",
    "9100": "IN Subscriber not found",
    "9101": "IN Max credit limit exceeded",
    "9102": "IN Below minimum balance",
    "9103": "IN Account not active",
    "9104": "IN Get balance and date not allowed",
    "9105": "IN Dedicated account not defined",
    "9106": "IN Periodic account management evaluation failed",
    "9017": "IN Other error",
    "122": "Fulfilment Failure from EIA",
    "141": "Invalid Shared Bolton offer Type",
    "154": "B number missing for Gift Bolton",
    "155": "Fulfilment Failure from EIA",
    "156": "INVALID REQUEST_ID",
    "157": "INVALID RULE ID",
    "158": "Fulfilment Failure from EIA",
    "182": "INVALID_VALIDATE (DCB)",
    "183": "INVALID_SMS_COUNTER",
    "184": "INVALID_VOICE_COUNTER",
    "185": "INVALID_DATA_COUNTER",
    "149": "Failure Response from Pre2Post Migration validation",
    "150": "Failure Response from Pre2Post Activation",
    "151": "INVALID PAYMENT_TYPE API parameter 163 SIM Swap Validation failure from EIA",
    "180": "Fulfilment Failure from EIA",
    "170": "Fulfilment Failure from EIA",
    "171": "INVALID_ACTIVATION_CODE in API Parameter",
    "172": "INVALID_ACTIVATION_NAME in API Parameter",
    "173": "INVALID_GET_POSTPAID_BILL",
    "174": "Fulfilment Failure from EIA",
    "162": "Failure response from Third party for final update 176 Tiara activation failure",
    "178": "INVALID_AUTO_RENEWAL_SERVICE",
    "197": "Fulfilment Failure from EIA",
    "198": "Fulfilment Failure from EIA",
    "204": "INVALID ACCOUNT NUMBER FOR CGATOPUP",
    "199": "Fulfilment Failure from EIA",
    "200": "Invalid Insurance ID",
    "201": "INVALID_INSURANCE_ORDER_REFERENCE_ID",
    "126": "Invalid dealer MSISDN",
    "127": "Invalid Dealer Pin",
    "128": "Invalid Profile Id",
    "129": "Failure response from e-Refill system",
    "165": "SERVICE_ACTIVATION_UNDER_PROCESS",
    "166": "VERIFICATION_UNDER_PROCESS",
    "167": "TEMPERED_DATA_OR_INVALID_RESPONSE",
    "168": "INVALID_RAW_DATA",
    "169": "INVALID_SIGNIN_DATA",
    "202": "Your request is already in queue",
    "205": "HTTP 500 ERROR CODE FOR BUYABLE PARENT",
    "212": "INVALID_RESPONSE_TDD_SYSTEM",
    "213": "INVALID_RESPONSE_FDD_SYSTEM",
    "214": "BANK_REFUND_FAILURE",
}
