    
def prepare_gift_bolton_multi_data(
    phone_number, 
    offer_details, 
    catalog_service,
    beneficiary_phone_dict, 
    count_beneficiary_phone_numbers, 
    language
    ):
    return {
        "item": count_beneficiary_phone_numbers,
        "service": catalog_service,
        "mobileNumber": phone_number,
        "amount": offer_details["price"],
        "parameters": {
        "parameter": [
          {
            "key": "offerCode",
            "value": beneficiary_phone_dict["offer_code"]
          },
          {
            "key": "BMobileNumber",
            "value": beneficiary_phone_dict["mobile_number"]
          },
          {
            "key": "offerDesc",
            "value": offer_details["title"][language]
          }
        ]
      }
    }
    