def mapper_transaction_history(transaction_history):
    for transaction in transaction_history["transactionList"]:
        offer_desc = transaction["offerDetails"]["offerDesc"]
        offer_amount = transaction["offerDetails"]["unitPrice"]
        paid_amount = transaction["paidAmount"]
        activation_offer_list = transaction["activationComponentList"]
    activation_service_msisdns = [
        activation_offer["serviceMsisdn"] for activation_offer in activation_offer_list
    ]
    response = {
        "offer_desc": offer_desc,
        "response_time": transaction_history["responseTime"],
        "offer_amount": offer_amount,
        "response_txn_id": transaction_history["responseTxnId"],
        "paid_amount": paid_amount,
        "gifted_msisdns": activation_service_msisdns,
    }
    return response
